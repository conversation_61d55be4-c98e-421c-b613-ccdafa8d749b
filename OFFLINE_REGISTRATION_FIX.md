# Perbaikan Reset WiFi & Registrasi Offline

## Masalah yang Diperbaiki

### 1. Reset WiFi Tidak Benar-Benar Menghapus Konfigurasi
❌ **Sebelumnya:**
- Konfigurasi WiFi masih tersimpan setelah reset
- Variabel tidak di-clear dengan benar
- Mode WiFi tidak di-reset

✅ **Setelah Diperbaiki:**
- Reset WiFi benar-benar menghapus semua konfigurasi
- Clear semua namespace Preferences
- Reset variabel dan flag dengan eksplisit

### 2. Registrasi Gagal Saat Mode AP (MQTT Not Connected)
❌ **Masalah:**
```
Card UID read successfully: 02:1E:AE:74:84:20:00
UID RFID terbaca: 02:1E:AE:74:84:20:00
MQTT not connected, cannot publish register
```

✅ **Setelah Diperbaiki:**
- Registrasi offline saat dalam Mode AP
- Data tersimpan di Preferences untuk sync nanti
- Auto-sync saat MQTT connect

## Perbaikan Detail

### 1. Reset WiFi yang Benar-Benar Kosong
```cpp
void resetPreferences() {
  // Stop semua service
  client.disconnect();
  server.end();
  dnsServer.stop();
  
  // Disconnect dengan force erase
  WiFi.disconnect(true, true);  // disconnect and erase stored credentials
  WiFi.softAPdisconnect(true);
  WiFi.mode(WIFI_OFF);
  
  // Hapus semua namespace preferences
  prefs.begin("wifi", false);
  prefs.clear();
  prefs.end();
  
  prefs.begin("system", false);
  prefs.clear();
  prefs.end();
  
  // Clear variabel eksplisit
  memset(wifiSSID, 0, sizeof(wifiSSID));
  memset(wifiPass, 0, sizeof(wifiPass));
  isAPMode = false;
  
  ESP.restart();
}
```

### 2. Registrasi Offline
```cpp
void publishRegister(const String& uuid, const String& method) {
  // Check if in AP mode or WiFi not connected
  if (isAPMode || WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi not connected (AP Mode), storing registration offline");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Registrasi OK");
    lcd.setCursor(0, 1);
    lcd.print("Mode Offline");
    buzz(1);
    
    // Store registration data for later sync
    storeOfflineRegistration(uuid, method);
    delay(2000);
    tampilkanSiapAbsen();
    return;
  }
  // ... rest of online logic
}
```

### 3. Penyimpanan Data Offline
```cpp
void storeOfflineRegistration(const String& uuid, const String& method) {
  prefs.begin("offline", false);
  
  // Get current count
  int count = prefs.getInt("reg_count", 0);
  
  // Store new registration
  String keyUuid = "reg_uuid_" + String(count);
  String keyMethod = "reg_method_" + String(count);
  String keyTime = "reg_time_" + String(count);
  
  prefs.putString(keyUuid.c_str(), uuid);
  prefs.putString(keyMethod.c_str(), method);
  prefs.putULong(keyTime.c_str(), millis());
  
  // Update count
  prefs.putInt("reg_count", count + 1);
  prefs.end();
}
```

### 4. Auto-Sync Saat MQTT Connect
```cpp
void syncOfflineData() {
  if (isAPMode || WiFi.status() != WL_CONNECTED || !client.connected()) {
    return; // Can't sync if not connected
  }
  
  prefs.begin("offline", true);
  int count = prefs.getInt("reg_count", 0);
  
  for (int i = 0; i < count; i++) {
    String uuid = prefs.getString(("reg_uuid_" + String(i)).c_str(), "");
    String method = prefs.getString(("reg_method_" + String(i)).c_str(), "rfid");
    
    if (uuid.length() > 0) {
      // Create payload with offline_sync flag
      JsonDocument doc;
      doc["uuid"] = uuid;
      doc["method"] = method;
      doc["timestamp"] = millis();
      doc["offline_sync"] = true;
      
      String payload;
      serializeJson(doc, payload);
      
      client.publish(topic_register, payload.c_str(), true);
    }
  }
  
  // Clear offline data after sync
  prefs.clear();
  prefs.end();
}
```

## Command Baru

### Serial Monitor Commands:
```
sync_offline     - Manual sync offline registration data
check_offline    - Check stored offline data count
reset_wifi       - Reset WiFi configuration (benar-benar kosong)
```

### Contoh Output:
```
check_offline
Offline registrations stored: 2
  #0: 02:1E:AE:74:84:20:00 (rfid)
  #1: 03:2F:BF:85:95:31:01 (rfid)

sync_offline
Syncing 2 offline registrations...
Syncing registration #0: 02:1E:AE:74:84:20:00
Successfully synced registration #0
Syncing registration #1: 03:2F:BF:85:95:31:01
Successfully synced registration #1
Offline data sync completed and cleared
```

## Skenario Penggunaan

### 1. Setup Awal (Mode AP)
1. ESP32 nyala → Mode AP aktif
2. Setup WiFi via web portal
3. Registrasi guru bisa dilakukan → Tersimpan offline
4. Setelah WiFi connect → Data auto-sync ke server

### 2. Registrasi Offline
1. Tap kartu RFID saat Mode AP
2. LCD: "Registrasi OK" + "Mode Offline"
3. Data tersimpan di Preferences
4. Saat MQTT connect → Auto-sync

### 3. Reset WiFi yang Benar
1. Command: `reset_wifi`
2. Semua konfigurasi benar-benar terhapus
3. ESP32 restart → Mode AP aktif
4. Setup WiFi dari awal

## Keunggulan Sistem

✅ **Offline Capability**: Registrasi tetap bisa dilakukan tanpa internet  
✅ **Auto-Sync**: Data offline otomatis sync saat connect  
✅ **Data Integrity**: Data tidak hilang meski offline  
✅ **Clean Reset**: Reset WiFi benar-benar menghapus semua  
✅ **Debug Tools**: Command untuk cek dan sync manual  

## Troubleshooting

### Jika Registrasi Tidak Tersimpan:
```
check_offline    # Cek data tersimpan
```

### Jika Data Tidak Sync:
```
sync_offline     # Manual sync
status           # Cek koneksi MQTT
```

### Jika Reset WiFi Tidak Bersih:
- Pastikan gunakan command `reset_wifi` (bukan reset fisik)
- Tunggu sampai restart selesai
- Cek dengan `status` untuk memastikan konfigurasi kosong
