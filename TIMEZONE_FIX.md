# Perbaikan Timezone Indonesia (WIB)

## Masalah
LCD menampilkan waktu 17:57 padahal sehar<PERSON>nya 00:57 WIB (selisih 7 jam).

## Perbaikan yang Diterapkan

### 1. Pengaturan Timezone Eksplisit
```cpp
void setDateTime() {
  // Set timezone untuk Indonesia (WIB = UTC+7)
  setenv("TZ", "WIB-7", 1);
  tzset();
  
  // Configure NTP dengan GMT offset
  configTime(7 * 3600, 0, "pool.ntp.org", "time.nist.gov");
  // ... rest of function
}
```

### 2. Debug Output Ditambahkan
- LCD akan menampilkan waktu lokal (WIB)
- Serial monitor akan menampilkan perbandingan waktu UTC dan WIB setiap 10 detik
- Fungsi debug untuk memverifikasi timezone

### 3. Command Baru untuk Sinkronisasi
Tambahkan command `sync_time` di serial monitor untuk memaksa sinkronisasi ulang:
```
sync_time
```

## Cara Mengatasi Masalah

### Langkah 1: Upload Firmware Baru
```bash
pio run --target upload
```

### Langkah 2: Monitor Serial
```bash
pio device monitor
```

### Langkah 3: Paksa Sinkronisasi (jika perlu)
Di serial monitor, ketik:
```
sync_time
```

### Langkah 4: Verifikasi
- Periksa output serial untuk melihat waktu UTC vs WIB
- Pastikan LCD menampilkan waktu WIB yang benar

## Troubleshooting

### Jika Masih Salah:
1. Restart ESP32 (tekan tombol reset)
2. Tunggu koneksi WiFi dan NTP sync
3. Gunakan command `sync_time` di serial monitor
4. Periksa koneksi internet ESP32

### Command Debug Tersedia:
- `status` - Cek status sistem
- `sync_time` - Paksa sinkronisasi NTP
- `toggle_modal` - Ubah mode REGISTRASI/ABSENSI
- `help` - Lihat semua command

## Fitur Mode Registrasi

### Tampilan LCD:
- **Mode Absensi**: "Siap Absen" + waktu
- **Mode Registrasi**: "Mode Registrasi" + waktu

### Serial Monitor:
- Menampilkan status mode saat startup
- Notifikasi otomatis saat mode berubah
- Status sistem setiap 10 detik dengan informasi mode

### Cara Mengubah Mode:
```
toggle_modal
```

## Catatan
- Timezone Indonesia (WIB) = UTC+7
- ESP32 akan otomatis sync dengan NTP server saat startup
- Mode registrasi untuk mendaftarkan guru baru
- Mode absensi untuk absensi harian
- Jika masih bermasalah, coba restart router/modem
