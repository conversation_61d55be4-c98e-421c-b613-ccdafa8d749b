# ESP32 MQTT Absensi - WiFi Manager & Timezone Fix

## Fitur Baru: WiFi Manager dengan Access Point Mode

### Cara Kerja Sistem WiFi
1. **Saat pertama kali dinyalakan** atau **tidak ada WiFi tersimpan**:
   - ESP32 akan masuk ke **Mode Access Point (AP)**
   - Nama AP: `ESP32-Absensi-[MAC_ADDRESS]`
   - IP Address: `***********`
   - LCD menampilkan: "Mode AP aktif" + IP Address

2. **Konfigurasi WiFi via Web Portal**:
   - Hubungkan perangkat (HP/laptop) ke WiFi ESP32
   - Buka browser, akses: `http://***********`
   - <PERSON><PERSON>h jaringan WiFi yang tersedia
   - Masukkan password WiFi
   - Klik "Save & Connect"
   - ESP32 akan restart dan terhubung ke WiFi baru

3. **Jika WiFi tersimpan tersedia**:
   - ESP32 akan otomatis terhubung ke WiFi tersimpan
   - <PERSON><PERSON> gagal, akan kembali ke Mode AP

## Perbaikan Timezone Indonesia (WIB)

### Masalah Sebelumnya
LCD menampilkan waktu 17:57 padahal seharusnya 00:57 WIB (selisih 7 jam).

## Perbaikan yang Diterapkan

### 1. Pengaturan Timezone Eksplisit
```cpp
void setDateTime() {
  // Set timezone untuk Indonesia (WIB = UTC+7)
  setenv("TZ", "WIB-7", 1);
  tzset();
  
  // Configure NTP dengan GMT offset
  configTime(7 * 3600, 0, "pool.ntp.org", "time.nist.gov");
  // ... rest of function
}
```

### 2. Debug Output Ditambahkan
- LCD akan menampilkan waktu lokal (WIB)
- Serial monitor akan menampilkan perbandingan waktu UTC dan WIB setiap 10 detik
- Fungsi debug untuk memverifikasi timezone

### 3. Command Baru untuk Sinkronisasi
Tambahkan command `sync_time` di serial monitor untuk memaksa sinkronisasi ulang:
```
sync_time
```

## Cara Mengatasi Masalah

### Langkah 1: Upload Firmware Baru
```bash
pio run --target upload
```

### Langkah 2: Monitor Serial
```bash
pio device monitor
```

### Langkah 3: Paksa Sinkronisasi (jika perlu)
Di serial monitor, ketik:
```
sync_time
```

### Langkah 4: Verifikasi
- Periksa output serial untuk melihat waktu UTC vs WIB
- Pastikan LCD menampilkan waktu WIB yang benar

## Troubleshooting

### Jika Masih Salah:
1. Restart ESP32 (tekan tombol reset)
2. Tunggu koneksi WiFi dan NTP sync
3. Gunakan command `sync_time` di serial monitor
4. Periksa koneksi internet ESP32

### Command Debug Tersedia:
- `status` - Cek status sistem (termasuk mode WiFi)
- `sync_time` - Paksa sinkronisasi NTP
- `start_ap` - Paksa masuk Mode AP untuk konfigurasi WiFi
- `reset_wifi` - Reset konfigurasi WiFi tersimpan
- `toggle_modal` - Ubah mode REGISTRASI/ABSENSI
- `help` - Lihat semua command

## Sistem WiFi Manager

### Fitur WiFi Manager:
- **Auto-connect** ke WiFi tersimpan
- **Access Point Mode** untuk konfigurasi awal
- **Web Portal** untuk setup WiFi yang mudah
- **WiFi Scanning** otomatis di web portal
- **Fallback** ke AP mode jika koneksi gagal
- **Reset WiFi** via serial command

### Status WiFi di Serial Monitor:
```
=== SYSTEM STATUS ===
WiFi Mode: Station Mode / AP Mode (Configuration)
WiFi: Connected / Disconnected
IP Address: ************* / AP IP: ***********
```

### Troubleshooting WiFi:
1. **Jika tidak bisa terhubung WiFi**:
   - Gunakan command `start_ap` di serial monitor
   - Atau reset ESP32 untuk masuk Mode AP otomatis

2. **Jika ingin ganti WiFi**:
   - Gunakan command `reset_wifi` di serial monitor
   - ESP32 akan restart dan masuk Mode AP

3. **Jika lupa password WiFi ESP32**:
   - Reset ESP32, akan otomatis masuk Mode AP
   - Nama AP selalu: `ESP32-Absensi-[MAC]`

## Fitur Mode Registrasi

### Tampilan LCD:
- **Mode Absensi**: "Siap Absen" + waktu
- **Mode Registrasi**: "Mode Registrasi" + waktu

### Serial Monitor:
- Menampilkan status mode saat startup
- Notifikasi otomatis saat mode berubah
- Status sistem setiap 10 detik dengan informasi mode

### Cara Mengubah Mode:
```
toggle_modal
```

## Catatan
- Timezone Indonesia (WIB) = UTC+7
- ESP32 akan otomatis sync dengan NTP server saat startup
- Mode registrasi untuk mendaftarkan guru baru
- Mode absensi untuk absensi harian
- Jika masih bermasalah, coba restart router/modem
