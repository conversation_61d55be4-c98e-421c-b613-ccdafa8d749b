#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <time.h>
#include <FS.h>
#include <LittleFS.h>
#include <SPI.h>
#include <MFRC522.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <HardwareSerial.h>
#include <ESPAsyncWebServer.h>
#include <DNSServer.h>

// ========================================
// PIN CONFIGURATION PER COMPONENT
// ========================================

// LCD I2C //
#define LCD_SDA_PIN 22        // ESP32 D22 → LCD (SDA)
#define LCD_SCL_PIN 21        // ESP32 D21 → LCD (SCL)
#define LCD_ADDRESS 0x27      // I2C Address

// MFRC522 RFID //
#define RFID_SS_PIN 5         // ESP32 D5 → RFID (SS/CS)
#define RFID_RST_PIN 4        // ESP32 D4 → RFID (RST)
#define RFID_SCK_PIN 18       // ESP32 D18 → RFID (SCK) - Default SPI
#define RFID_MISO_PIN 19      // ESP32 D19 → RFID (MISO) - Default SPI
#define RFID_MOSI_PIN 23      // ESP32 D23 → RFID (MOSI) - Default SPI

// BUZZER //
#define BUZZER_PIN 27         // ESP32 D27 → Buzzer

// LED //
#define LED_PIN 13            // ESP32 D13 → LED

// BUTTON //
#define RESET_BUTTON_PIN 26   // ESP32 D26 → Reset Button

// FINGERPRINT R503 //
#define FINGERPRINT_WAKEUP_PIN 2   // ESP32 D2 → Fingerprint Wakeup
#define FINGERPRINT_TX_PIN 17      // ESP32 GPIO 17 → Fingerprint RX (Serial2)
#define FINGERPRINT_RX_PIN 16      // ESP32 GPIO 16 → Fingerprint TX (Serial2)

// Constants
#define MAX_SSID_LEN 32
#define MAX_PASS_LEN 32

// --- KONFIGURASI WIFI DAN MQTT ---
const char* ssid = "Bisadong";
const char* password = "punyakamar14";
const char* mqtt_server = "ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud";
const char* mqtt_user = "absensi-sekolah";
const char* mqtt_pass = "Acekolah123";

// MQTT Topics
const char* topic_absensi = "absensi/data";
const char* topic_register = "absensi/register";
const char* topic_status = "absensi/status";
const char* topic_response = "absensi/response";

// Objects initialization
WiFiClientSecure espClient;
PubSubClient client(espClient);
MFRC522 mfrc522(RFID_SS_PIN, RFID_RST_PIN);
LiquidCrystal_I2C lcd(LCD_ADDRESS, 16, 2);
HardwareSerial fingerprintSerial(2); // Using Serial2 for fingerprint (avoid conflict)
Preferences prefs;

// Global variables
String uuid = "";
String fingerprintID = "";
bool isNFCTapped = false;
bool isFingerprintTapped = false;
bool resetFlag = false;
bool modalActive = false;
bool uuidTerdaftar = false;
bool fingerprintTerdaftar = false;
bool isDisplayingResult = false;

// Testing and Debug flags
bool ENABLE_FINGERPRINT = false;  // Set false to disable fingerprint
bool ENABLE_DUMMY_DATA = false;    // Set true to enable dummy data sending

// Timing variables
unsigned long lastNfcCheck = 0;
const unsigned long nfcInterval = 300; // Lebih cepat dari sebelumnya (300ms vs 500ms)
unsigned long lastFingerprintCheck = 0;
const unsigned long fingerprintInterval = 500; // Check fingerprint setiap 500ms
unsigned long resultDisplayTime = 0;
const unsigned long resultDisplayDuration = 3000;
unsigned long lastClockUpdate = 0;
const unsigned long clockUpdateInterval = 1000;

// Dummy data timing
unsigned long lastDummyData = 0;
const unsigned long dummyDataInterval = 10000; // Send dummy data every 10 seconds

// WiFi credentials storage
char wifiSSID[MAX_SSID_LEN] = {0};
char wifiPass[MAX_PASS_LEN] = {0};

// Custom characters for LCD icons
// Online icon (WiFi signal)
byte onlineIcon[] = {
  B01000,
  B11100,
  B01000,
  B01010,
  B01010,
  B00010,
  B00111,
  B00010
};

// Offline icon (X mark)
byte offlineIcon[] = {
  B00000,
  B10001,
  B01010,
  B00100,
  B01010,
  B10001,
  B00000,
  B00000
};

// WiFi Manager objects
AsyncWebServer server(80);
DNSServer dnsServer;
IPAddress local_IP(192, 168, 4, 1);
IPAddress gateway(192, 168, 4, 1);
IPAddress subnet(255, 255, 255, 0);
bool isAPMode = false;

// Function declarations
void buzz(int times, int duration = 100, int delayBetween = 100);
void tampilkanSiapAbsen();
void resetPreferences();
void checkResetButton();
void checkFingerprintWakeup();
void readWiFiConfigFromPrefs();
void saveWiFiConfigToPrefs(const char* ssid, const char* pass);
String getCardUID();
void handleNFCCard();
void initFingerprint();
void handleFingerprint();
String getFingerprintID();
void publishAbsensi(const String& uuid, const String& method = "rfid");
void publishRegister(const String& uuid, const String& method = "rfid");
void handleMQTTMessage(char* topic, byte* payload, unsigned int length);
void sendDummyData();
void handleDummyDataSending();
// WiFi Manager functions
bool isStoredSSIDAvailable(const char* ssid);
bool connectWiFi();
void startAPMode();
void setup_wifi_manager();
// Offline storage functions
void storeOfflineAbsensi(const String& uuid, const String& method);
void syncOfflineData();
// LCD custom character functions
void setupCustomCharacters();
void displayConnectionStatus();

// Utility Functions
void buzz(int times, int duration, int delayBetween) {
  for (int i = 0; i < times; i++) {
    digitalWrite(BUZZER_PIN, HIGH);
    delay(duration);
    digitalWrite(BUZZER_PIN, LOW);
    if (i < times - 1) delay(delayBetween);
  }
}

void tampilkanSiapAbsen() {
  lcd.clear();
  lcd.setCursor(0, 0);

  // Tampilkan mode berdasarkan status modal
  if (modalActive) {
    lcd.print("Mode Registrasi");
  } else {
    lcd.print("Siap Absen");
  }

  // Display connection status icon
  displayConnectionStatus();

  lcd.setCursor(0, 1);

  // Tampilkan waktu saat ini dalam WIB
  time_t now = time(nullptr);
  struct tm timeinfo;
  localtime_r(&now, &timeinfo);
  char timeStr[16];
  strftime(timeStr, sizeof(timeStr), "%H:%M:%S", &timeinfo);
  lcd.print(timeStr);

  // Debug: Print time info to serial
  static unsigned long lastDebugTime = 0;
  static bool lastModalState = false;

  // Print mode change immediately
  if (lastModalState != modalActive) {
    lastModalState = modalActive;
    Serial.println("=================================");
    Serial.println("MODE CHANGED:");
    Serial.println("- Mode: " + String(modalActive ? "REGISTRASI" : "ABSENSI"));
    Serial.println("- Status: " + String(modalActive ? "Siap untuk registrasi guru baru" : "Siap untuk absensi harian"));
    Serial.println("=================================");
  }

  if (millis() - lastDebugTime > 10000) { // Debug every 10 seconds
    lastDebugTime = millis();
    Serial.println("=== STATUS SISTEM ===");
    Serial.print("Mode: ");
    Serial.println(modalActive ? "REGISTRASI" : "ABSENSI");
    Serial.print("LCD Time (WIB): ");
    Serial.println(timeStr);

    // Also show UTC for comparison
    struct tm utcinfo;
    gmtime_r(&now, &utcinfo);
    char utcStr[16];
    strftime(utcStr, sizeof(utcStr), "%H:%M:%S", &utcinfo);
    Serial.print("UTC Time: ");
    Serial.println(utcStr);
    Serial.println("====================");
  }
}

void resetPreferences() {
  Serial.println("Reset Preferences: menghapus konfigurasi WiFi...");

  // Stop semua service
  client.disconnect();
  server.end();
  dnsServer.stop();

  // Disconnect dari semua koneksi WiFi dengan force
  WiFi.disconnect(true, true);  // disconnect and erase stored credentials
  WiFi.softAPdisconnect(true);
  WiFi.mode(WIFI_OFF);
  delay(2000);

  // Hapus konfigurasi dari Preferences dengan force clear
  prefs.begin("wifi", false);
  prefs.clear();
  prefs.end();

  // Hapus semua namespace preferences
  prefs.begin("system", false);
  prefs.clear();
  prefs.end();

  // Kosongkan variabel WiFi secara eksplisit
  memset(wifiSSID, 0, sizeof(wifiSSID));
  memset(wifiPass, 0, sizeof(wifiPass));

  // Reset flag
  isAPMode = false;

  Serial.println("=== RESET COMPLETE ===");
  Serial.println("- WiFi credentials erased");
  Serial.println("- Preferences cleared");
  Serial.println("- Variables reset");
  Serial.println("Restarting in 3 seconds...");

  delay(3000);
  ESP.restart();
}

void readWiFiConfigFromPrefs() {
  prefs.begin("wifi", true);
  prefs.getString("ssid", wifiSSID, MAX_SSID_LEN);
  prefs.getString("pass", wifiPass, MAX_PASS_LEN);
  prefs.end();
}

void saveWiFiConfigToPrefs(const char* ssid, const char* pass) {
  prefs.begin("wifi", false);
  prefs.putString("ssid", ssid);
  prefs.putString("pass", pass);
  prefs.end();
  Serial.println("WiFi config disimpan ke Preferences");
}

// WiFi Manager Functions
bool isStoredSSIDAvailable(const char* ssid) {
  Serial.println("Scanning WiFi networks...");
  int n = WiFi.scanNetworks();
  if (n == 0) {
    Serial.println("No networks found");
    return false;
  }

  for (int i = 0; i < n; ++i) {
    if (WiFi.SSID(i) == String(ssid)) {
      Serial.printf("SSID '%s' ditemukan dalam scan\n", ssid);
      return true;
    }
  }
  Serial.printf("SSID '%s' tidak ditemukan dalam scan\n", ssid);
  return false;
}

bool connectWiFi() {
  // Pastikan disconnect dari AP mode dan WiFi sebelumnya
  WiFi.softAPdisconnect(true);
  WiFi.disconnect(true);
  delay(1000);

  // Set ke mode Station (Client) saja
  WiFi.mode(WIFI_STA);
  delay(500);

  Serial.printf("Mencoba konek ke WiFi: %s\n", wifiSSID);
  WiFi.begin(wifiSSID, wifiPass);

  unsigned long startAttemptTime = millis();
  int attempts = 0;

  while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 15000) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Update LCD with connection status
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi Connecting");
    lcd.setCursor(0, 1);
    for (int i = 0; i < (attempts / 2) % 4; i++) {
      lcd.print(".");
    }
  }

  Serial.println();
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi berhasil konek!");
    Serial.print("IP Address: ");
    Serial.println(WiFi.localIP());

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi Connected");
    lcd.setCursor(0, 1);
    lcd.print(WiFi.localIP());
    delay(2000);

    isAPMode = false;
    return true;
  } else {
    Serial.println("Gagal konek WiFi");
    return false;
  }
}

void startAPMode() {
  // Pastikan disconnect dari Station mode dan WiFi sebelumnya
  WiFi.disconnect(true);
  delay(500);

  // Set ke mode Access Point saja
  WiFi.mode(WIFI_AP);
  delay(500);

  WiFi.softAPConfig(local_IP, gateway, subnet);

  // Generate unique AP name using MAC address
  uint64_t chipId = ESP.getEfuseMac();
  String apName = "ESP32-Absensi-" + String((uint32_t)(chipId >> 32), HEX) + String((uint32_t)chipId, HEX);
  WiFi.softAP(apName.c_str());

  dnsServer.start(53, "*", local_IP);
  isAPMode = true;

  lcd.clear();
  lcd.setCursor(0, 0);
  lcd.print("Mode AP aktif");
  lcd.setCursor(0, 1);
  lcd.print(local_IP);
  Serial.println("Mode AP aktif: " + apName);
  Serial.println("IP Address: " + local_IP.toString());

  // Setup web server routes
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request) {
    Serial.println("Root page requested");
    String html = "<!DOCTYPE html><html><head><title>ESP32 WiFi Config</title>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
    html += "<style>";
    html += "body{font-family:Arial;margin:40px;background-color:#f0f0f0;} ";
    html += ".container{max-width:400px;margin:auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);} ";
    html += "input{width:100%;padding:12px;margin:8px 0;box-sizing:border-box;border:1px solid #ddd;border-radius:4px;} ";
    html += "button{background-color:#4CAF50;color:white;padding:14px 20px;margin:8px 0;border:none;cursor:pointer;width:100%;border-radius:4px;font-size:16px;} ";
    html += "button:hover{background-color:#45a049;} ";
    html += ".network{padding:12px;border:1px solid #ddd;margin:5px 0;cursor:pointer;border-radius:4px;background:#f9f9f9;} ";
    html += ".network:hover{background-color:#e0e0e0;} ";
    html += ".loading{text-align:center;color:#666;} ";
    html += "h2{color:#333;text-align:center;} h3{color:#555;margin-top:20px;}";
    html += "</style></head><body>";
    html += "<div class='container'>";
    html += "<h2>ESP32 WiFi Configuration</h2>";
    html += "<div id='networks'><p class='loading'>Scanning networks...</p></div>";
    html += "<form action='/saveWiFi' method='POST' id='wifiForm'>";
    html += "<input type='text' name='ssid' id='ssidInput' placeholder='WiFi SSID' required maxlength='31'>";
    html += "<input type='password' name='password' id='passwordInput' placeholder='WiFi Password' maxlength='31'>";
    html += "<button type='submit'>Save & Connect</button>";
    html += "</form>";
    html += "<script>";
    html += "function loadNetworks() {";
    html += "  fetch('/scanWiFi')";
    html += "    .then(r => r.json())";
    html += "    .then(data => {";
    html += "      let html = '<h3>Available Networks:</h3>';";
    html += "      if(data.networks.length === 0) {";
    html += "        html += '<p>No networks found. Try refreshing.</p>';";
    html += "      } else {";
    html += "        data.networks.forEach(n => {";
    html += "          html += `<div class='network' onclick='selectNetwork(\"${n.ssid}\")'>${n.ssid} (${n.rssi}dBm)</div>`;";
    html += "        });";
    html += "      }";
    html += "      html += '<button onclick=\"loadNetworks()\" style=\"margin-top:10px;background:#2196F3;\">Refresh Networks</button>';";
    html += "      document.getElementById('networks').innerHTML = html;";
    html += "    })";
    html += "    .catch(err => {";
    html += "      document.getElementById('networks').innerHTML = '<p>Error loading networks. <button onclick=\"loadNetworks()\">Retry</button></p>';";
    html += "    });";
    html += "}";
    html += "function selectNetwork(ssid) {";
    html += "  document.getElementById('ssidInput').value = ssid;";
    html += "  document.getElementById('passwordInput').focus();";
    html += "}";
    html += "loadNetworks();";
    html += "</script>";
    html += "</div></body></html>";
    request->send(200, "text/html", html);
  });

  server.on("/scanWiFi", HTTP_GET, [](AsyncWebServerRequest *request) {
    Serial.println("Scan WiFi request received");

    // Clear any previous scan results
    WiFi.scanDelete();
    delay(100);

    // Start fresh scan
    Serial.println("Starting WiFi scan...");
    int n = WiFi.scanNetworks(false, true); // async=false, show_hidden=true
    Serial.printf("Found %d networks\n", n);

    String json = "{\"networks\":[";

    if (n > 0) {
      for (int i = 0; i < n; ++i) {
        if (i > 0) json += ",";

        String ssid = WiFi.SSID(i);
        int rssi = WiFi.RSSI(i);
        int encType = WiFi.encryptionType(i);

        // Skip empty SSID
        if (ssid.length() == 0) {
          ssid = "Hidden Network";
        }

        // Escape quotes and special characters in SSID
        ssid.replace("\"", "\\\"");
        ssid.replace("\\", "\\\\");

        json += "{";
        json += "\"ssid\":\"" + ssid + "\",";
        json += "\"rssi\":" + String(rssi) + ",";
        json += "\"encryption\":" + String(encType);
        json += "}";

        Serial.printf("Network %d: %s (%d dBm) [%s]\n",
                     i, ssid.c_str(), rssi,
                     encType == WIFI_AUTH_OPEN ? "Open" : "Secured");
      }
    }

    json += "]}";
    Serial.println("Sending scan results...");

    // Add CORS headers
    AsyncWebServerResponse *response = request->beginResponse(200, "application/json", json);
    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type");
    request->send(response);
  });

  server.on("/saveWiFi", HTTP_POST, [](AsyncWebServerRequest *request) {
    Serial.println("Save WiFi request received");

    if (request->hasParam("ssid", true) && request->hasParam("password", true)) {
      String ssid = request->getParam("ssid", true)->value();
      String password = request->getParam("password", true)->value();

      Serial.printf("Received SSID: '%s', Password length: %d\n", ssid.c_str(), password.length());

      if (ssid.length() > 0 && ssid.length() < MAX_SSID_LEN && password.length() < MAX_PASS_LEN) {
        // Clear arrays first
        memset(wifiSSID, 0, MAX_SSID_LEN);
        memset(wifiPass, 0, MAX_PASS_LEN);

        // Copy new values
        ssid.toCharArray(wifiSSID, MAX_SSID_LEN);
        password.toCharArray(wifiPass, MAX_PASS_LEN);

        Serial.printf("Saving SSID: '%s'\n", wifiSSID);
        saveWiFiConfigToPrefs(wifiSSID, wifiPass);

        request->send(200, "text/html",
          "<html><body style='font-family:Arial;text-align:center;margin-top:50px;'>"
          "<h2>WiFi Configuration Saved!</h2>"
          "<p>ESP32 will restart and connect to the new network.</p>"
          "<p>If connection fails, it will return to AP mode.</p>"
          "</body></html>");

        Serial.println("Restarting ESP32 in 2 seconds...");
        delay(2000);
        ESP.restart();
      } else {
        Serial.println("Invalid SSID or password length");
        request->send(400, "text/html",
          "<html><body style='font-family:Arial;text-align:center;margin-top:50px;'>"
          "<h2>Error!</h2><p>Invalid SSID or password length.</p>"
          "<p>SSID: 1-31 chars, Password: 0-31 chars</p>"
          "<a href='/'>Go Back</a></body></html>");
      }
    } else {
      Serial.println("Missing SSID or password parameters");
      request->send(400, "text/html",
        "<html><body style='font-family:Arial;text-align:center;margin-top:50px;'>"
        "<h2>Error!</h2><p>Missing SSID or password.</p>"
        "<a href='/'>Go Back</a></body></html>");
    }
  });

  // Add CORS headers for all requests
  server.onNotFound([](AsyncWebServerRequest *request) {
    if (request->method() == HTTP_OPTIONS) {
      request->send(200);
    } else {
      request->send(404, "text/plain", "Not found");
    }
  });

  server.begin();
  Serial.println("HTTP server started");
}

void setup_wifi_manager() {
  delay(10);

  // Read saved WiFi credentials
  readWiFiConfigFromPrefs();

  Serial.println("=== WiFi Manager Setup ===");
  Serial.printf("Stored SSID: '%s'\n", wifiSSID);
  Serial.printf("SSID Length: %d\n", strlen(wifiSSID));

  bool ssidAvailable = false;
  if (strlen(wifiSSID) > 0) {
    Serial.printf("Mengecek SSID tersimpan: %s\n", wifiSSID);
    ssidAvailable = isStoredSSIDAvailable(wifiSSID);
  } else {
    Serial.println("Tidak ada SSID tersimpan");
  }

  if (ssidAvailable) {
    Serial.println("SSID ditemukan, mencoba koneksi WiFi...");
    if (connectWiFi()) {
      Serial.println("WiFi Connected berhasil!");
      // connectWiFi() sudah set isAPMode = false
    } else {
      Serial.println("Koneksi WiFi gagal, mulai mode AP...");
      startAPMode();
    }
  } else {
    Serial.println("SSID tidak ditemukan atau tidak tersimpan, mulai mode AP...");
    startAPMode();
  }

  Serial.println("=== WiFi Manager Setup Complete ===");
}

// Offline Storage Functions
void storeOfflineAbsensi(const String& uuid, const String& method) {
  prefs.begin("offline", false);

  // Get current count of offline absensi
  int count = prefs.getInt("abs_count", 0);

  // Store new absensi
  String keyUuid = "abs_uuid_" + String(count);
  String keyMethod = "abs_method_" + String(count);
  String keyTime = "abs_time_" + String(count);

  prefs.putString(keyUuid.c_str(), uuid);
  prefs.putString(keyMethod.c_str(), method);
  prefs.putULong(keyTime.c_str(), millis());

  // Update count
  prefs.putInt("abs_count", count + 1);
  prefs.end();

  Serial.printf("Stored offline absensi #%d: %s (%s)\n", count, uuid.c_str(), method.c_str());
}

void syncOfflineData() {
  if (isAPMode || WiFi.status() != WL_CONNECTED || !client.connected()) {
    return; // Can't sync if not connected
  }

  prefs.begin("offline", true);
  int count = prefs.getInt("abs_count", 0);

  if (count == 0) {
    prefs.end();
    return; // No offline data to sync
  }

  Serial.printf("Syncing %d offline absensi...\n", count);

  for (int i = 0; i < count; i++) {
    String keyUuid = "abs_uuid_" + String(i);
    String keyMethod = "abs_method_" + String(i);

    String uuid = prefs.getString(keyUuid.c_str(), "");
    String method = prefs.getString(keyMethod.c_str(), "rfid");

    if (uuid.length() > 0) {
      Serial.printf("Syncing absensi #%d: %s\n", i, uuid.c_str());

      // Try to publish (without the offline check)
      if (client.connected()) {
        // Create absensi payload
        JsonDocument doc;
        doc["uuid"] = uuid;
        doc["method"] = method;
        doc["timestamp"] = millis();
        doc["offline_sync"] = true;

        String payload;
        serializeJson(doc, payload);

        bool success = client.publish(topic_absensi, payload.c_str(), true);
        if (success) {
          Serial.printf("Successfully synced absensi #%d\n", i);
        } else {
          Serial.printf("Failed to sync absensi #%d\n", i);
        }
        delay(100); // Small delay between publishes
      }
    }
  }

  prefs.end();

  // Clear offline data after successful sync
  prefs.begin("offline", false);
  prefs.clear();
  prefs.end();

  Serial.println("Offline absensi sync completed and cleared");
}

// LCD Custom Character Functions
void setupCustomCharacters() {
  lcd.createChar(0, onlineIcon);   // Character 0 = Online icon
  lcd.createChar(1, offlineIcon);  // Character 1 = Offline icon
  Serial.println("Custom characters loaded to LCD");
}

void displayConnectionStatus() {
  // Display connection status icon on LCD position (15, 0) - top right corner
  lcd.setCursor(15, 0);

  if (isAPMode || WiFi.status() != WL_CONNECTED) {
    lcd.write(1); // Display offline icon
  } else {
    lcd.write(0); // Display online icon
  }
}

// MFRC522 Functions with Error Handling
String getCardUID() {
  // Check if MFRC522 is responsive
  static unsigned long lastMFRC522Check = 0;
  static bool mfrc522Working = true;

  // Periodic health check every 60 seconds (reduced frequency)
  if (millis() - lastMFRC522Check > 60000) {
    lastMFRC522Check = millis();
    byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
    if (version == 0x00 || version == 0xFF) {
      if (mfrc522Working) { // Only show error once
        mfrc522Working = false;
        Serial.println("MFRC522 not responding (normal if not connected)");
      }
    } else {
      if (!mfrc522Working) { // Only show success once
        mfrc522Working = true;
        Serial.println("MFRC522 detected and working");
      }
    }
  }

  if (!mfrc522Working) {
    return "";
  }

  // Try to read card with timeout
  if (!mfrc522.PICC_IsNewCardPresent()) {
    return "";
  }

  if (!mfrc522.PICC_ReadCardSerial()) {
    Serial.println("Failed to read card serial");
    return "";
  }

  // Validate UID size
  if (mfrc522.uid.size == 0 || mfrc522.uid.size > 10) {
    Serial.println("Invalid UID size");
    mfrc522.PICC_HaltA();
    mfrc522.PCD_StopCrypto1();
    return "";
  }

  String uid = "";
  for (byte i = 0; i < mfrc522.uid.size; i++) {
    if (uid.length() > 0) uid += ":";
    if (mfrc522.uid.uidByte[i] < 0x10) uid += "0";
    uid += String(mfrc522.uid.uidByte[i], HEX);
  }
  uid.toUpperCase();

  // Properly halt the card
  mfrc522.PICC_HaltA();
  mfrc522.PCD_StopCrypto1();

  Serial.println("Card UID read successfully: " + uid);
  return uid;
}

void handleNFCCard() {
  String cardUID = getCardUID();
  if (cardUID.length() > 0) {
    uuid = cardUID;
    Serial.print("UID RFID terbaca: ");
    Serial.println(uuid);

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("UID RFID:");
    lcd.setCursor(0, 1);
    lcd.print(uuid.substring(0, 16)); // Tampilkan maksimal 16 karakter

    buzz(1);
    isNFCTapped = true;

    if (modalActive) {
      publishRegister(uuid, "rfid");
    } else {
      publishAbsensi(uuid, "rfid");
    }

    isDisplayingResult = true;
    resultDisplayTime = millis();
  }
}

// MQTT Functions with Error Handling
void publishAbsensi(const String& uuid, const String& method) {
  // Check if in AP mode or WiFi not connected - ABSENSI BISA OFFLINE
  if (isAPMode || WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi not connected (AP Mode), storing absensi offline");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Absensi OK");
    displayConnectionStatus(); // Show offline icon
    lcd.setCursor(0, 1);
    lcd.print("Mode Offline");
    buzz(1);

    // Store absensi data for later sync
    storeOfflineAbsensi(uuid, method);
    delay(2000);
    tampilkanSiapAbsen();
    return;
  }

  if (!client.connected()) {
    Serial.println("MQTT not connected, cannot publish absensi");
    lcd.clear();
    lcd.print("MQTT Offline");
    buzz(2);
    return;
  }

  // Validate UUID format (allow dummy data)
  if (uuid.length() < 5) {
    Serial.println("Invalid UUID format: " + uuid);
    lcd.clear();
    lcd.print("Invalid Card");
    buzz(2);
    return;
  }

  // Allow dummy data format (DUMMY:XX) and normal RFID format (XX:XX:XX:XX)
  if (!uuid.startsWith("DUMMY:") && uuid.length() < 8) {
    Serial.println("Invalid UUID format: " + uuid);
    lcd.clear();
    lcd.print("Invalid Card");
    buzz(2);
    return;
  }

  JsonDocument doc;
  doc["UUIDguru"] = uuid;
  doc["timestamp"] = time(nullptr);
  doc["action"] = "absensi";
  doc["method"] = method; // "rfid" or "fingerprint"
  doc["device_id"] = WiFi.macAddress();
  doc["rssi"] = WiFi.RSSI();

  String payload;
  size_t payloadSize = serializeJson(doc, payload);

  if (payloadSize == 0) {
    Serial.println("Failed to serialize JSON for absensi");
    lcd.clear();
    lcd.print("JSON Error");
    buzz(2);
    return;
  }

  Serial.println("Publishing absensi data...");
  Serial.println("Payload: " + payload);

  bool publishSuccess = false;
  int attempts = 0;

  while (!publishSuccess && attempts < 3) {
    attempts++;
    publishSuccess = client.publish(topic_absensi, payload.c_str(), true); // Retain message

    if (!publishSuccess) {
      Serial.println("Publish attempt " + String(attempts) + " failed");
      delay(100);
    }
  }

  if (publishSuccess) {
    Serial.println("Data absensi berhasil dikirim via MQTT");
    lcd.clear();
    lcd.print("Menunggu Server");
    buzz(1);
  } else {
    Serial.println("Gagal kirim data absensi setelah 3 percobaan");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Gagal Kirim");
    lcd.setCursor(0, 1);
    lcd.print("Coba Lagi");
    buzz(3, 150, 100);
  }
}

void publishRegister(const String& uuid, const String& method) {
  // Check if in AP mode or WiFi not connected - REGISTRASI TIDAK BISA OFFLINE
  if (isAPMode || WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi not connected (AP Mode), registrasi tidak dapat dilakukan offline");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Registrasi Gagal");
    displayConnectionStatus(); // Show offline icon
    lcd.setCursor(0, 1);
    lcd.print("Perlu WiFi Online");
    buzz(3, 150, 100);
    delay(3000);
    tampilkanSiapAbsen();
    return;
  }

  if (!client.connected()) {
    Serial.println("MQTT not connected, cannot publish register");
    lcd.clear();
    lcd.print("MQTT Offline");
    buzz(2);
    return;
  }

  // Validate UUID format
  if (uuid.length() < 8 || uuid.indexOf(":") == -1) {
    Serial.println("Invalid UUID format for registration: " + uuid);
    lcd.clear();
    lcd.print("Invalid Card");
    buzz(2);
    return;
  }

  JsonDocument doc;
  doc["UUIDguru"] = uuid;
  doc["timestamp"] = time(nullptr);
  doc["action"] = "register";
  doc["method"] = method; // "rfid" or "fingerprint"
  doc["device_id"] = WiFi.macAddress();
  doc["rssi"] = WiFi.RSSI();

  String payload;
  size_t payloadSize = serializeJson(doc, payload);

  if (payloadSize == 0) {
    Serial.println("Failed to serialize JSON for registration");
    lcd.clear();
    lcd.print("JSON Error");
    buzz(2);
    return;
  }

  Serial.println("Publishing registration data...");
  Serial.println("Payload: " + payload);

  bool publishSuccess = false;
  int attempts = 0;

  while (!publishSuccess && attempts < 3) {
    attempts++;
    publishSuccess = client.publish(topic_register, payload.c_str(), true); // Retain message

    if (!publishSuccess) {
      Serial.println("Register publish attempt " + String(attempts) + " failed");
      delay(100);
    }
  }

  if (publishSuccess) {
    Serial.println("UUID registrasi berhasil dikirim via MQTT");
    lcd.clear();
    lcd.print("Menunggu Server");
    buzz(1);
  } else {
    Serial.println("Gagal kirim UUID registrasi setelah 3 percobaan");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Gagal Daftar");
    lcd.setCursor(0, 1);
    lcd.print("Coba Lagi");
    buzz(3, 150, 100);
  }
}

void handleMQTTMessage(char* topic, byte* payload, unsigned int length) {
  String message = "";
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }

  Serial.print("MQTT message received [");
  Serial.print(topic);
  Serial.print("]: ");
  Serial.println(message);

  // Parse JSON response
  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, message);

  if (!error) {
    if (String(topic) == topic_response) {
      String action = doc["action"];
      String status = doc["status"];
      String responseUUID = doc["uuid"];

      if (responseUUID == uuid) { // Pastikan response untuk UUID yang sama
        if (action == "absensi") {
          if (status == "success") {
            lcd.clear();
            lcd.print("Absensi Berhasil");
            buzz(1);
          } else if (status == "already_present") {
            lcd.clear();
            lcd.setCursor(0, 0);
            lcd.print("Absensi sudah");
            lcd.setCursor(0, 1);
            lcd.print("lengkap");
            buzz(2);
          } else if (status == "not_registered") {
            lcd.clear();
            lcd.print("Belum Terdaftar");
            buzz(2);
          }
        } else if (action == "register") {
          if (status == "success") {
            lcd.clear();
            lcd.print("Registrasi OK");
            buzz(1);
          }
        }

        isDisplayingResult = true;
        resultDisplayTime = millis();
      }
    } else if (String(topic) == topic_status) {
      bool newModalStatus = doc["modal_active"];
      if (modalActive != newModalStatus) {
        modalActive = newModalStatus;
        Serial.print("Status modal diperbarui: ");
        Serial.println(modalActive ? "ACTIVE" : "INACTIVE");
      }
    }
  }

  // Blink LED untuk indikasi pesan diterima
  digitalWrite(LED_PIN, HIGH);
  delay(100);
  digitalWrite(LED_PIN, LOW);
}

// Fungsi setup_wifi() lama sudah digantikan dengan setup_wifi_manager()

void setDateTime() {
  // Set timezone for Indonesia (WIB = UTC+7)
  // Using timezone string format for better compatibility
  setenv("TZ", "WIB-7", 1);
  tzset();

  // Configure NTP with GMT offset in seconds (7 hours * 3600 seconds/hour = 25200)
  configTime(7 * 3600, 0, "pool.ntp.org", "time.nist.gov");
  Serial.print("Waiting for NTP time sync: ");
  time_t now = time(nullptr);
  while (now < 8 * 3600 * 2) {
    delay(500);
    Serial.print(".");
    now = time(nullptr);
  }
  Serial.println("");
  struct tm timeinfo;
  localtime_r(&now, &timeinfo); // Use localtime_r for local timezone
  Serial.print("Current time (WIB): ");
  Serial.print(asctime(&timeinfo));

  // Debug: Print both UTC and local time
  struct tm utcinfo;
  gmtime_r(&now, &utcinfo);
  Serial.print("UTC time: ");
  Serial.print(asctime(&utcinfo));
}

void callback(char* topic, byte* payload, unsigned int length) {
  handleMQTTMessage(topic, payload, length);
}

void reconnect() {
  static unsigned long lastReconnectAttempt = 0;
  static int reconnectAttempts = 0;

  // Don't attempt reconnection if in AP mode
  if (isAPMode) {
    return;
  }

  // Don't attempt reconnection too frequently
  if (millis() - lastReconnectAttempt < 5000) {
    return;
  }
  lastReconnectAttempt = millis();

  // Check WiFi connection first
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi disconnected, attempting to reconnect...");
    lcd.clear();
    lcd.print("WiFi Reconnect");

    WiFi.disconnect();
    WiFi.begin(strlen(wifiSSID) > 0 ? wifiSSID : ssid,
               strlen(wifiSSID) > 0 ? wifiPass : password);

    int wifiAttempts = 0;
    while (WiFi.status() != WL_CONNECTED && wifiAttempts < 10) {
      delay(500);
      wifiAttempts++;
      Serial.print(".");
    }

    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("WiFi reconnection failed");
      lcd.clear();
      lcd.print("WiFi Failed");
      return;
    } else {
      Serial.println("WiFi reconnected");
      lcd.clear();
      lcd.print("WiFi OK");
      delay(1000);
    }
  }

  if (!client.connected()) {
    Serial.print("Attempting MQTT connection... (attempt ");
    Serial.print(reconnectAttempts + 1);
    Serial.print(")");

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("MQTT Connecting");
    lcd.setCursor(0, 1);
    lcd.print("Attempt: ");
    lcd.print(reconnectAttempts + 1);

    String clientId = "ESP32-Absensi-";
    clientId += String(random(0xffff), HEX);

    if (client.connect(clientId.c_str(), mqtt_user, mqtt_pass)) {
      Serial.println(" connected");
      reconnectAttempts = 0; // Reset counter on successful connection

      // Subscribe to response and status topics
      if (client.subscribe(topic_response)) {
        Serial.println("Subscribed to response topic");
      } else {
        Serial.println("Failed to subscribe to response topic");
      }

      if (client.subscribe(topic_status)) {
        Serial.println("Subscribed to status topic");
      } else {
        Serial.println("Failed to subscribe to status topic");
      }

      // Publish device online status
      JsonDocument statusDoc;
      statusDoc["device"] = clientId;
      statusDoc["status"] = "online";
      statusDoc["timestamp"] = time(nullptr);
      statusDoc["ip"] = WiFi.localIP().toString();
      statusDoc["rssi"] = WiFi.RSSI();

      String statusPayload;
      serializeJson(statusDoc, statusPayload);

      if (client.publish(topic_status, statusPayload.c_str())) {
        Serial.println("Device status published");
      } else {
        Serial.println("Failed to publish device status");
      }

      // Sync offline data after successful MQTT connection
      syncOfflineData();

      lcd.clear();
      lcd.print("MQTT Connected");
      delay(1000);
      tampilkanSiapAbsen();

    } else {
      reconnectAttempts++;
      Serial.print(" failed, rc=");
      Serial.print(client.state());

      // Handle different error codes
      String errorMsg = "";
      switch (client.state()) {
        case -4: errorMsg = "Timeout"; break;
        case -3: errorMsg = "Lost connection"; break;
        case -2: errorMsg = "Network failed"; break;
        case -1: errorMsg = "Disconnected"; break;
        case 1: errorMsg = "Bad protocol"; break;
        case 2: errorMsg = "Bad client ID"; break;
        case 3: errorMsg = "Unavailable"; break;
        case 4: errorMsg = "Bad credentials"; break;
        case 5: errorMsg = "Unauthorized"; break;
        default: errorMsg = "Unknown"; break;
      }

      Serial.print(" (");
      Serial.print(errorMsg);
      Serial.println(")");

      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("MQTT Error:");
      lcd.setCursor(0, 1);
      lcd.print(errorMsg);

      // Restart if too many failed attempts
      if (reconnectAttempts >= 10) {
        Serial.println("Too many MQTT reconnection attempts. Restarting...");
        lcd.clear();
        lcd.print("MQTT Failed");
        lcd.setCursor(0, 1);
        lcd.print("Restarting...");
        delay(3000);
        ESP.restart();
      }
    }
  }
}

void checkResetButton() {
  static bool lastState = HIGH;
  static unsigned long pressStart = 0;
  static bool resetTriggered = false;

  bool currentState = digitalRead(RESET_BUTTON_PIN);

  if (lastState == HIGH && currentState == LOW) {
    pressStart = millis();
    resetTriggered = false;
  }

  if (currentState == LOW && !resetTriggered) {
    if (millis() - pressStart > 2000) {
      resetTriggered = true;
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("Reset WiFi Config");
      buzz(2, 150, 100);
      resetPreferences();
    }
  }

  if (lastState == LOW && currentState == HIGH) {
    resetTriggered = false;
  }

  lastState = currentState;
}

void checkFingerprintWakeup() {
  // Function untuk monitoring fingerprint wakeup pin
  if (!ENABLE_FINGERPRINT) return; // Skip if fingerprint disabled

  static bool lastWakeupState = HIGH;
  bool currentWakeupState = digitalRead(FINGERPRINT_WAKEUP_PIN);

  if (lastWakeupState == HIGH && currentWakeupState == LOW) {
    Serial.println("Fingerprint wakeup pin activated");
    // Trigger fingerprint reading
    handleFingerprint();
  }

  lastWakeupState = currentWakeupState;
}

// Dummy Data Functions
void sendDummyData() {
  static int dummyCounter = 1;

  String dummyUUID = "DUMMY:" + String(dummyCounter, HEX);
  dummyUUID.toUpperCase();

  Serial.println("=== SENDING DUMMY DATA ===");
  Serial.print("Dummy UUID: ");
  Serial.println(dummyUUID);

  lcd.clear();
  lcd.setCursor(0, 0);
  lcd.print("Dummy Data:");
  lcd.setCursor(0, 1);
  lcd.print(dummyUUID);

  buzz(1, 200); // Single long beep for dummy data

  if (modalActive) {
    publishRegister(dummyUUID, "dummy");
  } else {
    publishAbsensi(dummyUUID, "dummy");
  }

  isDisplayingResult = true;
  resultDisplayTime = millis();

  dummyCounter++;
  if (dummyCounter > 255) dummyCounter = 1; // Reset counter
}

void handleDummyDataSending() {
  if (ENABLE_DUMMY_DATA && millis() - lastDummyData > dummyDataInterval) {
    lastDummyData = millis();
    sendDummyData();
  }
}

// Fingerprint R503 Functions
void initFingerprint() {
  // Initialize fingerprint sensor with Serial2
  fingerprintSerial.begin(57600, SERIAL_8N1, FINGERPRINT_RX_PIN, FINGERPRINT_TX_PIN);
  delay(100);

  Serial.println("Initializing fingerprint sensor...");
  Serial.print("Fingerprint TX Pin: ");
  Serial.println(FINGERPRINT_TX_PIN);
  Serial.print("Fingerprint RX Pin: ");
  Serial.println(FINGERPRINT_RX_PIN);

  // Send test command to check if sensor is responding
  uint8_t testCmd[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x01, 0x00, 0x05};
  fingerprintSerial.write(testCmd, sizeof(testCmd));

  delay(100);

  if (fingerprintSerial.available()) {
    Serial.println("Fingerprint sensor detected");
    while (fingerprintSerial.available()) {
      fingerprintSerial.read(); // Clear buffer
    }
  } else {
    Serial.println("Fingerprint sensor not responding (normal if not connected)");
  }
}

String getFingerprintID() {
  // Simple fingerprint reading implementation
  // This is a basic implementation - you may want to use a dedicated library

  // Send command to capture fingerprint
  uint8_t captureCmd[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x01, 0x00, 0x05};
  fingerprintSerial.write(captureCmd, sizeof(captureCmd));

  delay(1000); // Wait for capture

  if (fingerprintSerial.available()) {
    String response = "";
    while (fingerprintSerial.available()) {
      response += String(fingerprintSerial.read(), HEX);
    }

    // Basic response parsing - implement proper protocol parsing
    if (response.length() > 0) {
      // Generate a simple ID based on response
      String fingerprintID = "FP:" + String(millis() % 10000, HEX);
      fingerprintID.toUpperCase();
      return fingerprintID;
    }
  }

  return "";
}

void handleFingerprint() {
  String fingerprintID = getFingerprintID();
  if (fingerprintID.length() > 0) {
    uuid = fingerprintID;
    Serial.print("Fingerprint ID detected: ");
    Serial.println(fingerprintID);

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Fingerprint:");
    lcd.setCursor(0, 1);
    lcd.print(fingerprintID.substring(0, 16)); // Tampilkan maksimal 16 karakter

    buzz(2, 100, 50); // Different buzz pattern for fingerprint
    isFingerprintTapped = true;

    if (modalActive) {
      publishRegister(fingerprintID, "fingerprint");
    } else {
      publishAbsensi(fingerprintID, "fingerprint");
    }

    isDisplayingResult = true;
    resultDisplayTime = millis();
  }
}

void setup() {
  Serial.begin(9600); // Kembali ke 9600 untuk kompatibilitas yang lebih baik
  delay(1000); // Wait for serial to initialize
  Serial.println();
  Serial.println("=================================");
  Serial.println("ESP32 MQTT Absensi System");
  Serial.println("=================================");

  // Initialize pins dengan konfigurasi baru
  pinMode(LED_PIN, OUTPUT);                    // D13 → LED
  pinMode(BUZZER_PIN, OUTPUT);                 // D27 → Buzzer
  pinMode(RESET_BUTTON_PIN, INPUT_PULLUP);     // D26 → Reset Button
  pinMode(FINGERPRINT_WAKEUP_PIN, INPUT_PULLUP); // D2 → Fingerprint Wakeup
  digitalWrite(LED_PIN, LOW);
  digitalWrite(BUZZER_PIN, LOW);

  // Initialize I2C and SPI
  Wire.begin(LCD_SDA_PIN, LCD_SCL_PIN);
  SPI.begin(RFID_SCK_PIN, RFID_MISO_PIN, RFID_MOSI_PIN, RFID_SS_PIN);

  // Initialize LCD
  lcd.init();
  lcd.backlight();

  // Setup custom characters for icons
  setupCustomCharacters();

  lcd.clear();
  lcd.print("Menghubungkan...");

  // Initialize MFRC522
  mfrc522.PCD_Init();
  Serial.println("MFRC522 initialized");

  // Check MFRC522 version
  byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
  Serial.print("MFRC522 Software Version: 0x");
  Serial.println(version, HEX);

  // Initialize Fingerprint (conditional)
  if (ENABLE_FINGERPRINT) {
    initFingerprint();
  } else {
    Serial.println("Fingerprint sensor DISABLED for testing");
  }

  // Read WiFi config from preferences
  readWiFiConfigFromPrefs();

  // Setup WiFi with Manager
  setup_wifi_manager();
  setDateTime();

  // Initialize filesystem
  if (!LittleFS.begin(true)) {
    Serial.println("An Error has occurred while mounting LittleFS");
    return;
  }

  // Load SSL certificate
  File cert = LittleFS.open("/certs.ar");
  if (!cert) {
    Serial.println("Failed to open certs.ar file. Did you upload the filesystem image?");
  } else {
    Serial.println("Successfully opened cert file from LittleFS.");
    espClient.setCACert(cert.readString().c_str());
    cert.close();
  }

  // Setup MQTT
  client.setServer(mqtt_server, 8883);
  client.setCallback(callback);

  // Initial display
  tampilkanSiapAbsen();

  Serial.println("=================================");
  Serial.println("ESP32 MQTT Absensi System Ready!");
  Serial.println("=================================");
  Serial.println("Configuration:");
  Serial.println("- Fingerprint: " + String(ENABLE_FINGERPRINT ? "ENABLED" : "DISABLED"));
  Serial.println("- Dummy Data: " + String(ENABLE_DUMMY_DATA ? "ENABLED (10s interval)" : "DISABLED"));
  Serial.println("- Mode: " + String(modalActive ? "REGISTRASI" : "ABSENSI"));
  Serial.println("- Status: " + String(modalActive ? "Siap untuk registrasi guru baru" : "Siap untuk absensi harian"));
  Serial.println();
  Serial.println("Available Commands:");
  Serial.println("- toggle_modal: Ubah mode REGISTRASI/ABSENSI");
  Serial.println("- sync_time: Sinkronisasi waktu NTP");
  Serial.println("- status: Lihat status sistem");
  Serial.println("- help: Lihat semua command");
  Serial.println("=================================");
}

void loop() {
  // Handle DNS server for AP mode
  if (isAPMode) {
    dnsServer.processNextRequest();
  }

  // Check MQTT connection (only if not in AP mode)
  if (!isAPMode) {
    if (!client.connected()) {
      reconnect();
    }
    client.loop();
  }

  // Check reset button
  checkResetButton();

  // Check fingerprint wakeup pin (conditional)
  if (ENABLE_FINGERPRINT) {
    checkFingerprintWakeup();
  }

  // Check for NFC/RFID cards (non-blocking, optimized timing)
  if (millis() - lastNfcCheck > nfcInterval) {
    lastNfcCheck = millis();
    handleNFCCard();
  }

  // Check for fingerprint (non-blocking, conditional)
  if (ENABLE_FINGERPRINT && millis() - lastFingerprintCheck > fingerprintInterval) {
    lastFingerprintCheck = millis();
    // Only check if no current activity
    if (!isDisplayingResult && !isNFCTapped) {
      handleFingerprint();
    }
  }

  // Handle dummy data sending
  handleDummyDataSending();

  // Handle result display timeout
  if (isDisplayingResult && millis() - resultDisplayTime >= resultDisplayDuration) {
    tampilkanSiapAbsen();
    isDisplayingResult = false;
  }

  // Update clock display
  if (!isDisplayingResult && millis() - lastClockUpdate > clockUpdateInterval) {
    lastClockUpdate = millis();
    tampilkanSiapAbsen(); // Refresh time display
  }

  // Handle serial commands for testing (optional)
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim();

    Serial.println("Received command: '" + command + "'"); // Debug output

    if (command.startsWith("test_uuid:")) {
      String testUUID = command.substring(10);
      Serial.println("Testing with UUID: " + testUUID);
      uuid = testUUID;
      publishAbsensi(testUUID, "test");
    } else if (command.startsWith("test_fingerprint:")) {
      String testFP = command.substring(17);
      Serial.println("Testing with Fingerprint: " + testFP);
      fingerprintID = testFP;
      publishAbsensi(testFP, "fingerprint");
    } else if (command == "toggle_modal") {
      modalActive = !modalActive;
      Serial.println("=================================");
      Serial.println("MODE BERUBAH:");
      Serial.println("- Mode: " + String(modalActive ? "REGISTRASI" : "ABSENSI"));
      Serial.println("- Status: " + String(modalActive ? "Siap untuk registrasi guru baru" : "Siap untuk absensi harian"));
      Serial.println("=================================");

      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("Mode Berubah:");
      lcd.setCursor(0, 1);
      lcd.print(modalActive ? "REGISTRASI" : "ABSENSI");
      delay(2000);
      tampilkanSiapAbsen();
    } else if (command == "reset_wifi") {
      resetPreferences();
    } else if (command == "sync_time") {
      Serial.println("Force syncing time with NTP...");
      lcd.clear();
      lcd.print("Syncing Time...");
      setDateTime();
      tampilkanSiapAbsen();
    } else if (command == "start_ap") {
      Serial.println("Starting AP mode for WiFi configuration...");
      Serial.println("Disconnecting from current WiFi...");
      WiFi.disconnect(true);
      delay(1000);
      startAPMode();
    } else if (command == "sync_offline") {
      Serial.println("Manual sync offline data...");
      syncOfflineData();
    } else if (command == "check_offline") {
      prefs.begin("offline", true);
      int count = prefs.getInt("abs_count", 0);
      Serial.printf("Offline absensi stored: %d\n", count);
      for (int i = 0; i < count; i++) {
        String keyUuid = "abs_uuid_" + String(i);
        String keyMethod = "abs_method_" + String(i);
        String uuid = prefs.getString(keyUuid.c_str(), "");
        String method = prefs.getString(keyMethod.c_str(), "");
        Serial.printf("  #%d: %s (%s)\n", i, uuid.c_str(), method.c_str());
      }
      prefs.end();
    } else if (command == "send_dummy") {
      Serial.println("Sending dummy data manually...");
      sendDummyData();
    } else if (command == "enable_dummy") {
      ENABLE_DUMMY_DATA = true;
      lastDummyData = millis(); // Reset timer
      Serial.println("Dummy data auto-sending ENABLED (every 10 seconds)");
    } else if (command == "disable_dummy") {
      ENABLE_DUMMY_DATA = false;
      Serial.println("Dummy data auto-sending DISABLED");
    } else if (command == "enable_fingerprint") {
      ENABLE_FINGERPRINT = true;
      Serial.println("Fingerprint sensor ENABLED");
      initFingerprint();
    } else if (command == "disable_fingerprint") {
      ENABLE_FINGERPRINT = false;
      Serial.println("Fingerprint sensor DISABLED");
    } else if (command == "status") {
      Serial.println("=== SYSTEM STATUS ===");
      Serial.println("WiFi Mode: " + String(isAPMode ? "AP Mode (Configuration)" : "Station Mode"));
      Serial.println("WiFi: " + String(WiFi.status() == WL_CONNECTED ? "Connected" : "Disconnected"));
      if (isAPMode) {
        Serial.println("AP IP: " + WiFi.softAPIP().toString());
      } else if (WiFi.status() == WL_CONNECTED) {
        Serial.println("IP Address: " + WiFi.localIP().toString());
      }
      Serial.println("MQTT: " + String(client.connected() ? "Connected" : "Disconnected"));
      Serial.println("Modal Mode: " + String(modalActive ? "REGISTRATION" : "ATTENDANCE"));
      Serial.println("Fingerprint: " + String(ENABLE_FINGERPRINT ? "ENABLED" : "DISABLED"));
      Serial.println("Dummy Data: " + String(ENABLE_DUMMY_DATA ? "ENABLED" : "DISABLED"));
      Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    } else if (command == "help") {
      Serial.println("=== AVAILABLE COMMANDS ===");
      Serial.println("test_uuid:A1:B2:C3:D4     - Test RFID with specific UUID");
      Serial.println("test_fingerprint:FP:1234  - Test fingerprint with specific ID");
      Serial.println("send_dummy                - Send dummy data once");
      Serial.println("enable_dummy              - Enable auto dummy data (10s interval)");
      Serial.println("disable_dummy             - Disable auto dummy data");
      Serial.println("enable_fingerprint        - Enable fingerprint sensor");
      Serial.println("disable_fingerprint       - Disable fingerprint sensor");
      Serial.println("toggle_modal              - Toggle registration/attendance mode");
      Serial.println("sync_time                 - Force sync time with NTP server");
      Serial.println("start_ap                  - Start AP mode for WiFi config");
      Serial.println("sync_offline              - Sync offline absensi data");
      Serial.println("check_offline             - Check stored offline absensi");
      Serial.println("status                    - Show system status");
      Serial.println("reset_wifi                - Reset WiFi configuration");
      Serial.println("help                      - Show this help");
    } else if (command.length() > 0) {
      Serial.println("Unknown command: '" + command + "'");
      Serial.println("Type 'help' for available commands");
    }
  }
}