# WiFi Setup Guide - ESP32 MQTT Absensi

## Langkah Setup WiFi Pertama Kali

### 1. Nyalakan ESP32
- ESP32 akan otomatis masuk **Mode Access Point (AP)**
- LCD menampilkan: "Mode AP aktif" + IP (***********)
- Serial monitor menampilkan nama AP: `ESP32-Absensi-[MAC_ADDRESS]`

### 2. Hubungkan Perangkat ke ESP32
- Buka WiFi di HP/laptop
- Cari dan hubungkan ke: `ESP32-Absensi-[MAC_ADDRESS]`
- Tidak perlu password

### 3. Buka Web Portal
- Buka browser di perangkat yang terhubung
- Akses: `http://***********`
- Halaman konfigurasi WiFi akan terbuka

### 4. Pilih dan Konfigurasi WiFi
- Tunggu scan jaringan WiFi selesai
- Klik nama WiFi yang ingin digunakan (otomatis mengisi SSID)
- Atau ketik manual SSID WiFi
- Masukkan password WiFi
- <PERSON><PERSON> "Save & Connect"

### 5. ESP32 Restart dan Terhubung
- ESP32 akan restart otomatis
- Mencoba terhubung ke WiFi yang dikonfigurasi
- Jika berhasil: LCD menampilkan "WiFi Connected" + IP Address
- Jika gagal: Kembali ke Mode AP untuk konfigurasi ulang

## Command Serial Monitor

### Untuk Troubleshooting:
```
start_ap        - Paksa masuk Mode AP
reset_wifi      - Reset konfigurasi WiFi
status          - Lihat status WiFi dan sistem
sync_time       - Sinkronisasi waktu NTP
help            - Lihat semua command
```

### Contoh Output Status:
```
=== SYSTEM STATUS ===
WiFi Mode: Station Mode
WiFi: Connected
IP Address: *************
MQTT: Connected
Mode: ABSENSI
```

## Troubleshooting

### Masalah: ESP32 tidak bisa terhubung WiFi
**Solusi:**
1. Pastikan password WiFi benar
2. Pastikan WiFi 2.4GHz (bukan 5GHz)
3. Gunakan command `reset_wifi` untuk setup ulang

### Masalah: Tidak bisa akses web portal
**Solusi:**
1. Pastikan terhubung ke WiFi ESP32
2. Coba akses: `http://***********`
3. Restart ESP32 jika perlu

### Masalah: Ingin ganti WiFi
**Solusi:**
1. Gunakan command `reset_wifi` di serial monitor
2. ESP32 akan restart dan masuk Mode AP
3. Ulangi langkah konfigurasi WiFi

## Catatan Penting
- ESP32 hanya mendukung WiFi 2.4GHz
- Konfigurasi WiFi tersimpan di memori internal
- Mode AP otomatis aktif jika tidak ada WiFi tersimpan
- Nama AP unik berdasarkan MAC address ESP32
